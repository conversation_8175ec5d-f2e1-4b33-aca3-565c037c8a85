import { Property, EvaluationResults } from '@/types';

export interface ComparisonCriteria {
  id: string;
  name: string;
  weight: number;
  category: 'financial' | 'location' | 'development' | 'risk';
  getValue: (property: Property, evaluation?: EvaluationResults) => number | string;
  compare: (value1: any, value2: any) => 'better' | 'worse' | 'equal';
}

export interface PropertyComparison {
  id: string;
  properties: Property[];
  evaluations: (EvaluationResults | null)[];
  criteria: ComparisonCriteria[];
  results: ComparisonResult[];
  summary: ComparisonSummary;
  createdAt: Date;
  createdBy: string;
}

export interface ComparisonResult {
  criteriaId: string;
  values: any[];
  rankings: number[];
  winner: number; // Index of best property
}

export interface ComparisonSummary {
  overallWinner: number;
  categoryWinners: Record<string, number>;
  strengths: Record<number, string[]>;
  weaknesses: Record<number, string[]>;
  recommendations: string[];
}

export class PropertyComparisonEngine {
  private static defaultCriteria: ComparisonCriteria[] = [
    // Financial Criteria
    {
      id: 'assessed-value',
      name: 'Assessed Value',
      weight: 0.2,
      category: 'financial',
      getValue: (property) => property.assessedValue,
      compare: (v1, v2) => v1 < v2 ? 'better' : v1 > v2 ? 'worse' : 'equal'
    },
    {
      id: 'price-per-sqft',
      name: 'Price per Sq Ft',
      weight: 0.15,
      category: 'financial',
      getValue: (property) => property.assessedValue / property.lotSize,
      compare: (v1, v2) => v1 < v2 ? 'better' : v1 > v2 ? 'worse' : 'equal'
    },
    {
      id: 'tax-burden',
      name: 'Annual Tax Burden',
      weight: 0.1,
      category: 'financial',
      getValue: (property) => property.taxAmount,
      compare: (v1, v2) => v1 < v2 ? 'better' : v1 > v2 ? 'worse' : 'equal'
    },

    // Development Criteria
    {
      id: 'lot-size',
      name: 'Lot Size',
      weight: 0.15,
      category: 'development',
      getValue: (property) => property.lotSize,
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    },
    {
      id: 'density-potential',
      name: 'Density Potential',
      weight: 0.2,
      category: 'development',
      getValue: (property, evaluation) => evaluation?.criteria.densityPotential?.totalUnits || 0,
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    },
    {
      id: 'height-allowance',
      name: 'Height Allowance',
      weight: 0.1,
      category: 'development',
      getValue: (property, evaluation) => evaluation?.criteria.heightRestrictions?.totalHeight || 0,
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    },

    // Location Criteria
    {
      id: 'transit-access',
      name: 'Transit Access Score',
      weight: 0.15,
      category: 'location',
      getValue: (property, evaluation) => {
        const transit = evaluation?.criteria.transitAccess;
        if (!transit) return 0;
        return transit.withinQuarterMile ? 100 - (transit.nearestStopDistance * 100) : 0;
      },
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    },

    // Risk Criteria
    {
      id: 'environmental-risk',
      name: 'Environmental Risk',
      weight: 0.1,
      category: 'risk',
      getValue: (property, evaluation) => evaluation?.criteria.environmentalConcerns?.hasIssues ? 0 : 100,
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    },
    {
      id: 'historical-designation',
      name: 'Historical Designation Risk',
      weight: 0.05,
      category: 'risk',
      getValue: (property, evaluation) => evaluation?.criteria.historicalStatus?.hasDesignation ? 0 : 100,
      compare: (v1, v2) => v1 > v2 ? 'better' : v1 < v2 ? 'worse' : 'equal'
    }
  ];

  /**
   * Compare multiple properties using specified criteria
   */
  static compareProperties(
    properties: Property[],
    evaluations: (EvaluationResults | null)[],
    customCriteria?: ComparisonCriteria[],
    userId?: string
  ): PropertyComparison {
    if (properties.length < 2) {
      throw new Error('At least 2 properties are required for comparison');
    }

    const criteria = customCriteria || this.defaultCriteria;
    const results: ComparisonResult[] = [];

    // Calculate comparison results for each criteria
    for (const criterion of criteria) {
      const values = properties.map((property, index) => 
        criterion.getValue(property, evaluations[index] || undefined)
      );

      const rankings = this.calculateRankings(values, criterion.compare);
      const winner = rankings.indexOf(1); // Index of rank 1 (best)

      results.push({
        criteriaId: criterion.id,
        values,
        rankings,
        winner
      });
    }

    // Calculate overall summary
    const summary = this.calculateSummary(properties, criteria, results);

    return {
      id: `comparison_${Date.now()}`,
      properties,
      evaluations,
      criteria,
      results,
      summary,
      createdAt: new Date(),
      createdBy: userId || 'anonymous'
    };
  }

  /**
   * Calculate rankings for a set of values
   */
  private static calculateRankings(values: any[], compareFunction: (v1: any, v2: any) => 'better' | 'worse' | 'equal'): number[] {
    const indexed = values.map((value, index) => ({ value, index }));
    
    // Sort by comparison function
    indexed.sort((a, b) => {
      const comparison = compareFunction(a.value, b.value);
      return comparison === 'better' ? -1 : comparison === 'worse' ? 1 : 0;
    });

    // Assign rankings
    const rankings = new Array(values.length);
    indexed.forEach((item, rank) => {
      rankings[item.index] = rank + 1;
    });

    return rankings;
  }

  /**
   * Calculate overall comparison summary
   */
  private static calculateSummary(
    properties: Property[],
    criteria: ComparisonCriteria[],
    results: ComparisonResult[]
  ): ComparisonSummary {
    const propertyCount = properties.length;
    const categoryScores: Record<string, number[]> = {};
    const overallScores = new Array(propertyCount).fill(0);

    // Calculate weighted scores
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const criterion = criteria[i];

      if (!result || !criterion) continue;

      if (!categoryScores[criterion.category]) {
        categoryScores[criterion.category] = new Array(propertyCount).fill(0);
      }

      for (let j = 0; j < propertyCount; j++) {
        if (result.rankings && result.rankings[j] !== undefined) {
          const score = (propertyCount - result.rankings[j] + 1) * criterion.weight;
          overallScores[j] += score;
          const categoryArray = categoryScores[criterion.category];
          if (categoryArray) {
            categoryArray[j] += score;
          }
        }
      }
    }

    // Find winners
    const overallWinner = overallScores.indexOf(Math.max(...overallScores));
    const categoryWinners: Record<string, number> = {};
    
    for (const [category, scores] of Object.entries(categoryScores)) {
      categoryWinners[category] = scores.indexOf(Math.max(...scores));
    }

    // Generate strengths and weaknesses
    const strengths: Record<number, string[]> = {};
    const weaknesses: Record<number, string[]> = {};

    for (let i = 0; i < propertyCount; i++) {
      strengths[i] = [];
      weaknesses[i] = [];

      for (let j = 0; j < results.length; j++) {
        const result = results[j];
        const criterion = criteria[j];

        if (!result || !criterion) continue;

        if (result.winner === i) {
          strengths[i]?.push(criterion.name);
        } else if (result.rankings && result.rankings[i] === propertyCount) {
          weaknesses[i]?.push(criterion.name);
        }
      }
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(properties, results, criteria, overallWinner);

    return {
      overallWinner,
      categoryWinners,
      strengths,
      weaknesses,
      recommendations
    };
  }

  /**
   * Generate recommendations based on comparison results
   */
  private static generateRecommendations(
    properties: Property[],
    results: ComparisonResult[],
    criteria: ComparisonCriteria[],
    overallWinner: number
  ): string[] {
    const recommendations: string[] = [];
    const winner = properties[overallWinner];

    if (!winner) {
      return ['No clear winner could be determined from the comparison.'];
    }

    recommendations.push(
      `Property at ${winner.address} is the overall best choice based on the selected criteria.`
    );

    // Find the biggest advantages
    const winnerAdvantages = results
      .filter(result => result.winner === overallWinner)
      .map(result => criteria.find(c => c.id === result.criteriaId)?.name)
      .filter(Boolean);

    if (winnerAdvantages.length > 0) {
      recommendations.push(
        `Key advantages: ${winnerAdvantages.slice(0, 3).join(', ')}.`
      );
    }

    // Identify close alternatives
    const scores = new Array(properties.length).fill(0);
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const criterion = criteria[i];

      if (!result || !criterion || !result.rankings) continue;

      for (let j = 0; j < properties.length; j++) {
        if (result.rankings[j] !== undefined) {
          scores[j] += (properties.length - result.rankings[j] + 1) * criterion.weight;
        }
      }
    }

    const sortedIndices = scores
      .map((score, index) => ({ score, index }))
      .sort((a, b) => b.score - a.score);

    const firstPlace = sortedIndices[0];
    const secondPlace = sortedIndices[1];

    if (sortedIndices.length > 1 && firstPlace && secondPlace &&
        firstPlace.score - secondPlace.score < 0.1) {
      const runnerUp = properties[secondPlace.index];
      if (runnerUp) {
        recommendations.push(
          `${runnerUp.address} is a close second choice and worth considering.`
        );
      }
    }

    return recommendations;
  }

  /**
   * Export comparison to different formats
   */
  static exportComparison(comparison: PropertyComparison, format: 'json' | 'csv' | 'excel'): string | object {
    switch (format) {
      case 'json':
        return comparison;
      
      case 'csv':
        return this.exportToCSV(comparison);
      
      case 'excel':
        return this.exportToExcel(comparison);
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private static exportToCSV(comparison: PropertyComparison): string {
    const headers = ['Criteria', ...comparison.properties.map((p, i) => `Property ${i + 1}`)];
    const rows = [headers];

    for (let i = 0; i < comparison.criteria.length; i++) {
      const criterion = comparison.criteria[i];
      const result = comparison.results[i];

      if (!criterion || !result) continue;

      const row = [criterion.name, ...result.values.map(v => String(v))];
      rows.push(row);
    }

    return rows.map(row => row.join(',')).join('\n');
  }

  private static exportToExcel(comparison: PropertyComparison): object {
    // This would integrate with a library like xlsx
    return {
      sheets: {
        'Comparison': {
          data: comparison.results.map((result, i) => {
            const criterion = comparison.criteria[i];
            if (!criterion) return null;

            return {
              criteria: criterion.name,
              ...comparison.properties.reduce((acc, p, j) => {
                acc[`property_${j + 1}`] = result.values[j];
                return acc;
              }, {} as Record<string, any>)
            };
          }).filter(Boolean)
        },
        'Summary': {
          data: {
            overallWinner: comparison.properties[comparison.summary.overallWinner]?.address || 'Unknown',
            recommendations: comparison.summary.recommendations
          }
        }
      }
    };
  }
}
