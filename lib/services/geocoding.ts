/**
 * Geocoding Service
 * Handles address geocoding and reverse geocoding using Google Maps API
 */

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface AddressComponents {
  streetNumber?: string;
  streetName?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  formattedAddress?: string;
}

export interface GeocodeResult {
  coordinates: Coordinates;
  addressComponents: AddressComponents;
  placeId?: string;
  accuracy?: string;
}

export interface AddressSuggestion {
  description: string;
  placeId: string;
  mainText: string;
  secondaryText: string;
}

class GeocodingService {
  private geocoder: google.maps.Geocoder | null = null;
  private autocompleteService: google.maps.places.AutocompleteService | null = null;
  private placesService: google.maps.places.PlacesService | null = null;

  constructor() {
    if (typeof window !== 'undefined' && window.google) {
      this.initializeServices();
    }
  }

  private initializeServices() {
    if (window.google) {
      this.geocoder = new google.maps.Geocoder();
      this.autocompleteService = new google.maps.places.AutocompleteService();
      
      // Create a dummy div for PlacesService
      const dummyDiv = document.createElement('div');
      this.placesService = new google.maps.places.PlacesService(dummyDiv);
    }
  }

  /**
   * Geocode an address to get coordinates and detailed address components
   */
  async geocodeAddress(address: string): Promise<GeocodeResult> {
    if (!this.geocoder) {
      throw new Error('Geocoder not initialized. Make sure Google Maps API is loaded.');
    }

    return new Promise((resolve, reject) => {
      this.geocoder!.geocode(
        { address },
        (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            const result = results[0];
            const location = result.geometry.location;
            
            const addressComponents = this.parseAddressComponents(result.address_components);
            
            resolve({
              coordinates: {
                lat: location.lat(),
                lng: location.lng(),
              },
              addressComponents: {
                ...addressComponents,
                formattedAddress: result.formatted_address,
              },
              placeId: result.place_id,
              accuracy: result.geometry.location_type,
            });
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        }
      );
    });
  }

  /**
   * Reverse geocode coordinates to get address information
   */
  async reverseGeocode(coordinates: Coordinates): Promise<GeocodeResult> {
    if (!this.geocoder) {
      throw new Error('Geocoder not initialized. Make sure Google Maps API is loaded.');
    }

    return new Promise((resolve, reject) => {
      this.geocoder!.geocode(
        { location: coordinates },
        (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            const result = results[0];
            const addressComponents = this.parseAddressComponents(result.address_components);
            
            resolve({
              coordinates,
              addressComponents: {
                ...addressComponents,
                formattedAddress: result.formatted_address,
              },
              placeId: result.place_id,
            });
          } else {
            reject(new Error(`Reverse geocoding failed: ${status}`));
          }
        }
      );
    });
  }

  /**
   * Get address suggestions for autocomplete
   */
  async getAddressSuggestions(input: string, options?: {
    types?: string[];
    componentRestrictions?: google.maps.places.ComponentRestrictions;
    bounds?: google.maps.LatLngBounds;
  }): Promise<AddressSuggestion[]> {
    if (!this.autocompleteService) {
      throw new Error('Autocomplete service not initialized. Make sure Google Maps API is loaded.');
    }

    return new Promise((resolve, reject) => {
      this.autocompleteService!.getPlacePredictions(
        {
          input,
          types: options?.types || ['address'],
          componentRestrictions: options?.componentRestrictions || { country: 'us' },
          ...(options?.bounds && { bounds: options.bounds }),
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            const suggestions = predictions.map(prediction => ({
              description: prediction.description,
              placeId: prediction.place_id,
              mainText: prediction.structured_formatting.main_text,
              secondaryText: prediction.structured_formatting.secondary_text,
            }));
            resolve(suggestions);
          } else if (status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
            resolve([]);
          } else {
            reject(new Error(`Autocomplete failed: ${status}`));
          }
        }
      );
    });
  }

  /**
   * Get detailed place information by place ID
   */
  async getPlaceDetails(placeId: string): Promise<GeocodeResult> {
    if (!this.placesService) {
      throw new Error('Places service not initialized. Make sure Google Maps API is loaded.');
    }

    return new Promise((resolve, reject) => {
      this.placesService!.getDetails(
        {
          placeId,
          fields: ['geometry', 'address_components', 'formatted_address', 'place_id'],
        },
        (place, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && place) {
            if (!place.geometry?.location) {
              reject(new Error('No geometry found for place'));
              return;
            }

            const location = place.geometry.location;
            const addressComponents = place.address_components 
              ? this.parseAddressComponents(place.address_components)
              : {};
            
            resolve({
              coordinates: {
                lat: location.lat(),
                lng: location.lng(),
              },
              addressComponents: {
                ...addressComponents,
                formattedAddress: place.formatted_address,
              },
              placeId: place.place_id,
            });
          } else {
            reject(new Error(`Place details failed: ${status}`));
          }
        }
      );
    });
  }

  /**
   * Parse Google Maps address components into a structured format
   */
  private parseAddressComponents(components: google.maps.GeocoderAddressComponent[]): AddressComponents {
    const result: AddressComponents = {};

    components.forEach(component => {
      const types = component.types;

      if (types.includes('street_number')) {
        result.streetNumber = component.long_name;
      } else if (types.includes('route')) {
        result.streetName = component.long_name;
      } else if (types.includes('locality')) {
        result.city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        result.state = component.short_name;
      } else if (types.includes('postal_code')) {
        result.zipCode = component.long_name;
      } else if (types.includes('country')) {
        result.country = component.long_name;
      }
    });

    return result;
  }

  /**
   * Calculate distance between two coordinates (in miles)
   */
  calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 3959; // Earth's radius in miles
    const dLat = this.toRadians(coord2.lat - coord1.lat);
    const dLng = this.toRadians(coord2.lng - coord1.lng);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(coord1.lat)) * Math.cos(this.toRadians(coord2.lat)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Check if the service is ready to use
   */
  isReady(): boolean {
    return !!(this.geocoder && this.autocompleteService && this.placesService);
  }

  /**
   * Initialize the service (call this after Google Maps API is loaded)
   */
  initialize(): void {
    this.initializeServices();
  }
}

// Export a singleton instance
export const geocodingService = new GeocodingService();

// Export utility functions
export const formatAddress = (components: AddressComponents): string => {
  const parts = [];
  
  if (components.streetNumber && components.streetName) {
    parts.push(`${components.streetNumber} ${components.streetName}`);
  } else if (components.streetName) {
    parts.push(components.streetName);
  }
  
  if (components.city) {
    parts.push(components.city);
  }
  
  if (components.state && components.zipCode) {
    parts.push(`${components.state} ${components.zipCode}`);
  } else if (components.state) {
    parts.push(components.state);
  }
  
  return parts.join(', ');
};

export const isValidCoordinates = (coordinates: Coordinates): boolean => {
  return (
    typeof coordinates.lat === 'number' &&
    typeof coordinates.lng === 'number' &&
    coordinates.lat >= -90 &&
    coordinates.lat <= 90 &&
    coordinates.lng >= -180 &&
    coordinates.lng <= 180
  );
};
